import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:foodcorp_admin/shared/excel_import.dart';
import 'package:intl/intl.dart';

class UserModel {
  final String docId;
  final DateTime? createdAt;
  final String name;
  final num cpfNo;
  final String districtoffice;
  final String? email;
  final String? phoneNo;
  final num? settlement;
  final num? totalSubs;
  final num? totalSubsInt;
  final num? ltLoansDue;
  final num? stLoansDue;
  final num? totalLtLoans;
  final num? totalStLoans;
  final num? totalLtIntPaid;
  final num? totalStIntPaid;
  final num? totalDivident;
  final num? totalShares;
  final DateTime? registrationDate;

  final num employeeNo;
  final String? currentAddress;
  final String? permanentAddress;
  final String? documents;
  final bool approved;
  final String password;
  final Map<String, dynamic>? userPrevoiusMonthlyRecord;
  final Map<String, dynamic>? userPrevoiusYearlyRecord;

  final String? bankAcName;
  final String? bankName;
  final String? ifscCode;
  final num? bankAcNo;

  final bool archived;
  final bool subsIntPayoutStatus;
  final bool dividendPayoutStatus;

  final num? momento;
  final String? nomineeName;
  final String? nomineeRelation;

  final num? obSubs;
  final num? obShares;
  final num? obLt;
  final num? obSt;

  UserModel({
    required this.docId,
    required this.createdAt,
    required this.name,
    required this.cpfNo,
    required this.districtoffice,
    required this.email,
    required this.phoneNo,
    required this.settlement,
    required this.totalSubs,
    required this.totalSubsInt,
    required this.ltLoansDue,
    required this.stLoansDue,
    required this.totalLtLoans,
    required this.totalStLoans,
    required this.totalLtIntPaid,
    required this.totalStIntPaid,
    required this.totalDivident,
    required this.totalShares,
    required this.registrationDate,
    required this.employeeNo,
    required this.currentAddress,
    required this.permanentAddress,
    required this.documents,
    required this.approved,
    required this.password,
    required this.userPrevoiusMonthlyRecord,
    required this.userPrevoiusYearlyRecord,
    required this.bankAcName,
    required this.bankName,
    required this.ifscCode,
    required this.bankAcNo,
    required this.archived,
    required this.subsIntPayoutStatus,
    required this.dividendPayoutStatus,
    required this.momento,
    required this.nomineeName,
    required this.nomineeRelation,
    required this.obSubs,
    required this.obShares,
    required this.obLt,
    required this.obSt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    DateTime? safeParseDate(dynamic value) {
      if (value == null) return null;
      if (value is Timestamp) return value.toDate();
      if (value is DateTime) return value;
      if (value is String) {
        try {
          return DateTime.tryParse(value) ??
              DateFormat('dd.MM.yyyy').parse(value);
        } catch (_) {
          return null;
        }
      }
      return null;
    }

    return UserModel(
      docId: json['docId'],
      createdAt: safeParseDate(json['createdAt']),
      name: json['name'],
      cpfNo: json['cpfNo'],
      districtoffice: json['districtoffice'],
      email: json['email'],
      phoneNo: json['phonenumber'],
      settlement: json['settlement'],
      totalSubs: json['totalSubs'],
      totalSubsInt: json['totalSubsInt'],
      ltLoansDue: json['ltLoansDue'],
      stLoansDue: json['stLoansDue'],
      totalLtLoans: json['totalLtLoans'],
      totalStLoans: json['totalStLoans'],
      totalLtIntPaid: json['totalLtIntPaid'],
      totalStIntPaid: json['totalStIntPaid'],
      totalDivident: json['totalDivident'],
      totalShares: json['totalShares'],
      registrationDate: safeParseDate(json['registrationDate']),
      employeeNo: json['employeeNo'],
      currentAddress: json['currentAddress'],
      permanentAddress: json['permanentAddress'],
      documents: json['documents'] ?? "",
      approved: json['approved'],
      password: json['password'],
      userPrevoiusMonthlyRecord:
          Map<String, dynamic>.from(json['userPrevoiusMonthlyRecord'] ?? {}),
      userPrevoiusYearlyRecord:
          Map<String, dynamic>.from(json['userPrevoiusYearlyRecord'] ?? {}),
      bankAcName: json['bankAcName'],
      bankName: json['bankName'],
      ifscCode: json['ifscCode'],
      bankAcNo: json['bankAcNo'],
      archived: json['archived'],
      subsIntPayoutStatus: json['subsIntPayoutStatus'] ?? false,
      dividendPayoutStatus: json['dividendPayoutStatus'] ?? false,
      momento: json['momento'],
      nomineeName: json['nomineeName'],
      nomineeRelation: json['nomineeRelation'],
      obSubs: json['obSubs'],
      obShares: json['obShares'],
      obLt: json['obLt'],
      obSt: json['obSt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'name': name,
      'cpfNo': cpfNo,
      'districtoffice': districtoffice,
      'email': email,
      'phonenumber': phoneNo,
      'settlement': settlement,
      'totalSubs': totalSubs,
      'totalSubsInt': totalSubsInt,
      'ltLoansDue': ltLoansDue,
      'stLoansDue': stLoansDue,
      'totalLtLoans': totalLtLoans,
      'totalStLoans': totalStLoans,
      'totalLtIntPaid': totalLtIntPaid,
      'totalStIntPaid': totalStIntPaid,
      'totalDivident': totalDivident,
      'totalShares': totalShares,
      'registrationDate': registrationDate != null
          ? Timestamp.fromDate(registrationDate!)
          : null,
      'employeeNo': employeeNo,
      'currentAddress': currentAddress,
      'permanentAddress': permanentAddress,
      'documents': documents,
      'approved': approved,
      'password': password,
      'userPrevoiusMonthlyRecord': userPrevoiusMonthlyRecord,
      'userPrevoiusYearlyRecord': userPrevoiusYearlyRecord,
      'bankAcName': bankAcName,
      'bankName': bankName,
      'ifscCode': ifscCode,
      'bankAcNo': bankAcNo,
      'archived': archived,
      'subsIntPayoutStatus': subsIntPayoutStatus,
      'dividendPayoutStatus': dividendPayoutStatus,
      'momento': momento,
      'nomineeName': nomineeName,
      'nomineeRelation': nomineeRelation,
      'obSubs': obSubs,
      'obShares': obShares,
      'obLt': obLt,
      'obSt': obSt,
    };
  }

  factory UserModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    DateTime? safeParseDate(dynamic value) {
      if (value == null) return null;
      if (value is Timestamp) return value.toDate();
      if (value is DateTime) return value;
      if (value is String) {
        try {
          return DateTime.tryParse(value) ??
              DateFormat('dd.MM.yyyy').parse(value);
        } catch (_) {
          return null;
        }
      }
      return null;
    }

    return UserModel(
      docId: snap.id,
      createdAt: safeParseDate(data['createdAt']),
      name: data['name'],
      cpfNo: data['cpfNo'],
      districtoffice: data['districtoffice'],
      email: data['email'],
      phoneNo: data['phonenumber'],
      settlement: data['settlement'],
      totalSubs: data['totalSubs'],
      totalSubsInt: data['totalSubsInt'],
      ltLoansDue: data['ltLoansDue'],
      stLoansDue: data['stLoansDue'],
      totalLtLoans: data['totalLtLoans'],
      totalStLoans: data['totalStLoans'],
      totalLtIntPaid: data['totalLtIntPaid'],
      totalStIntPaid: data['totalStIntPaid'],
      totalDivident: data['totalDivident'],
      totalShares: data['totalShares'],
      registrationDate: safeParseDate(data['registrationDate']),
      employeeNo: data['employeeNo'],
      currentAddress: data['currentAddress'],
      permanentAddress: data['permanentAddress'],
      documents: data['documents'],
      approved: data['approved'],
      password: data['password'],
      userPrevoiusMonthlyRecord: data['userPrevoiusMonthlyRecord'] is Map
          ? Map<String, dynamic>.from(data['userPrevoiusMonthlyRecord'])
          : {},
      userPrevoiusYearlyRecord: data['userPrevoiusYearlyRecord'] is Map
          ? Map<String, dynamic>.from(data['userPrevoiusYearlyRecord'])
          : {},
      bankAcName: data['bankAcName'],
      bankName: data['bankName'],
      ifscCode: data['ifscCode'],
      bankAcNo: data['bankAcNo'],
      archived: data['archived'],
      subsIntPayoutStatus: data['subsIntPayoutStatus'] ?? false,
      dividendPayoutStatus: data['dividendPayoutStatus'] ?? false,
      momento: data['momento'],
      nomineeName: data['nomineeName'],
      nomineeRelation: data['nomineeRelation'],
      obSubs: data['obSubs'],
      obShares: data['obShares'],
      obLt: data['obLt'],
      obSt: data['obSt'],
    );
  }

  factory UserModel.fromExcelRow(List<dynamic> row, int cpfNo) {
    DateTime? safeParseDate(dynamic value) {
      try {
        if (value == null) return null;
        if (value is Timestamp) return value.toDate();
        if (value is DateTime) return value;
        if (value is String) {
          return DateTime.tryParse(value) ??
              DateFormat('dd.MM.yyyy').parse(value);
        }
        return null;
      } catch (_) {
        return null;
      }
    }

    // Safe access from row
    dynamic v(int i) => extractCellValue(i >= row.length ? null : row[i]);

    return UserModel(
      docId: '',
      createdAt: DateTime.now(),
      name: v(2).toString(),
      cpfNo: cpfNo,
      employeeNo: num.tryParse(v(1).toString()) ?? 0,
      currentAddress: v(4)?.toString(),
      permanentAddress: v(5)?.toString(),
      districtoffice: v(6)?.toString() ?? '',
      phoneNo: v(7)?.toString(),
      email: v(8)?.toString().toLowerCase(),
      bankAcName: v(9)?.toString(),
      bankName: v(10)?.toString(),
      bankAcNo: num.tryParse(v(11)?.toString() ?? '0'),
      ifscCode: v(12)?.toString(),
      totalStLoans: num.tryParse(v(13)?.toString() ?? '0'),
      stLoansDue: num.tryParse(v(15)?.toString() ?? '0'),
      totalStIntPaid: num.tryParse(v(16)?.toString() ?? '0'),
      totalLtLoans: num.tryParse(v(17)?.toString() ?? '0'),
      ltLoansDue: num.tryParse(v(19)?.toString() ?? '0'),
      totalLtIntPaid: num.tryParse(v(20)?.toString() ?? '0'),
      totalSubs: ((num.tryParse(v(21)?.toString() ?? '0') ?? 0) +
          (num.tryParse(v(28)?.toString() ?? '0') ?? 0)), //april
      //     +
      //     (num.tryParse(v(28)?.toString() ?? '0') ?? 0) + //may
      //     (num.tryParse(v(28)?.toString() ?? '0') ?? 0) + //june
      //     (num.tryParse(v(28)?.toString() ?? '0') ?? 0) + //july
      //     (num.tryParse(v(28)?.toString() ?? '0') ?? 0), //august
      totalShares: num.tryParse(v(22)?.toString() ?? '0'),
      registrationDate: safeParseDate(v(39)),
      approved: true,
      documents: '',
      password: '12345678',
      archived: false,
      subsIntPayoutStatus: false,
      dividendPayoutStatus: false,
      settlement: null,
      totalSubsInt: null,
      totalDivident: null,
      userPrevoiusMonthlyRecord: {},
      userPrevoiusYearlyRecord: {},
      momento: null,
      nomineeName: null,
      nomineeRelation: null,
      obSubs: (num.tryParse(v(21)?.toString() ?? '0') ?? 0),
      obShares: (num.tryParse(v(22)?.toString() ?? '0') ?? 0),
      obLt: (num.tryParse(v(23)?.toString() ?? '0') ?? 0),
      obSt: (num.tryParse(v(24)?.toString() ?? '0') ?? 0),
    );
  }
}

class InputUserModel {
  final String docId;
  final DateTime createdAt;
  final String name;
  final num cpfNo;
  final String? districtoffice;
  final String email;
  final String? phoneNo;
  final num? settlement;
  final num? totalSubs; // Subs = Subscription
  final num? totalSubsInt; // Int = Interest
  final num? ltLoansDue; // lt = longterm
  final num? stLoansDue; // st = shortTerm
  final num? totalLtLoans;
  final num? totalStLoans;
  final num? totalLtIntPaid;
  final num? totalStIntPaid;
  final num? totalDivident;
  final num? totalShares;
  final DateTime? registrationDate;

  final num employeeNo;
  final String currentAddress;
  final String permanentAddress;
  final String documents;
  final bool approved;
  final String password;
  final Map<String, dynamic>? userPrevoiusMonthlyRecord;
  final Map<String, dynamic>? userPrevoiusYearlyRecord;
  final bool archived;

  InputUserModel({
    required this.docId,
    required this.createdAt,
    required this.name,
    required this.cpfNo,
    required this.districtoffice,
    required this.email,
    required this.phoneNo,
    required this.settlement,
    required this.totalSubs,
    required this.totalSubsInt,
    required this.ltLoansDue,
    required this.stLoansDue,
    required this.totalLtLoans,
    required this.totalStLoans,
    required this.totalLtIntPaid,
    required this.totalStIntPaid,
    required this.totalDivident,
    required this.totalShares,
    this.registrationDate,
    required this.employeeNo,
    required this.currentAddress,
    required this.permanentAddress,
    required this.documents,
    required this.approved,
    required this.password,
    required this.userPrevoiusMonthlyRecord,
    required this.userPrevoiusYearlyRecord,
    required this.archived,
  });

  // Factory constructor to create an instance from JSON
  factory InputUserModel.fromJson(Map<String, dynamic> json) {
    return InputUserModel(
        docId: json['docId'],
        createdAt: DateTime.parse(json['createdAt']),
        name: json['name'],
        cpfNo: json['cpfNo'],
        districtoffice: json['districtoffice'],
        email: json['email'],
        phoneNo: json['phonenumber'],
        settlement: json['settlement'],
        totalSubs: json['totalSubs'],
        totalSubsInt: json['totalSubsInt'],
        ltLoansDue: json['ltLoansDue'],
        stLoansDue: json['stLoansDue'],
        totalLtLoans: json['totalLtLoans'],
        totalStLoans: json['totalStLoans'],
        totalLtIntPaid: json['totalLtIntPaid'],
        totalStIntPaid: json['totalStIntPaid'],
        totalDivident: json['totalDivident'],
        totalShares: json['totalShares'],
        registrationDate: json['registrationDate'] != null
            ? DateTime.parse(json['registrationDate'])
            : null,
        employeeNo: json['employeeNo'],
        currentAddress: json['currentAddress'],
        permanentAddress: json['permanentAddress'],
        documents: json['documents'],
        approved: json['approved'],
        password: json['password'],
        userPrevoiusMonthlyRecord:
            Map<String, dynamic>.from(json['userPrevoiusMonthlyRecord']),
        userPrevoiusYearlyRecord:
            Map<String, dynamic>.from(json['userPrevoiusYearlyRecord']),
        archived: json['archived']);
  }

  // Convert this instance to a map (JSON format)
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt.toIso8601String(),
      'name': name,
      'cpfNo': cpfNo,
      'districtoffice': districtoffice,
      'email': email,
      'phonenumber': phoneNo,
      'settlement': settlement,
      'totalSubs': totalSubs,
      'totalSubsInt': totalSubsInt,
      'ltLoansDue': ltLoansDue,
      'stLoansDue': stLoansDue,
      'totalLtLoans': totalLtLoans,
      'totalStLoans': totalStLoans,
      'totalLtIntPaid': totalLtIntPaid,
      'totalStIntPaid': totalStIntPaid,
      'totalDivident': totalDivident,
      'totalShares': totalShares,
      'registrationDate': registrationDate,
      'employeeNo': employeeNo,
      'currentAddress': currentAddress,
      'permanentAddress': permanentAddress,
      'documents': documents,
      'approved': approved,
      'password': password,
      'userPrevoiusMonthlyRecord': userPrevoiusMonthlyRecord,
      'userPrevoiusYearlyRecord': userPrevoiusYearlyRecord,
      'archived': archived,
    };
  }

  // Convert a Firestore DocumentSnapshot to a UserModel
  factory InputUserModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return InputUserModel(
      docId: snap.id,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      name: data['name'],
      cpfNo: data['cpfNo'],
      districtoffice: data['districtoffice'],
      email: data['email'],
      phoneNo: data['phonenumber'],
      settlement: data['settlement'],
      totalSubs: data['totalSubs'],
      totalSubsInt: data['totalSubsInt'],
      ltLoansDue: data['ltLoansDue'],
      stLoansDue: data['stLoansDue'],
      totalLtLoans: data['totalLtLoans'],
      totalStLoans: data['totalStLoans'],
      totalLtIntPaid: data['totalLtIntPaid'],
      totalStIntPaid: data['totalStIntPaid'],
      totalDivident: data['totalDivident'],
      totalShares: data['totalShares'],
      registrationDate: data['registrationDate'] != null
          ? (data['registrationDate'] as Timestamp).toDate()
          : null,
      employeeNo: data['employeeNo'],
      currentAddress: data['currentAddress'],
      permanentAddress: data['permanentAddress'],
      documents: data['documents'],
      userPrevoiusMonthlyRecord:
          Map<String, dynamic>.from(data['userPrevoiusMonthlyRecord'] ?? {}),
      userPrevoiusYearlyRecord:
          Map<String, dynamic>.from(data['userPrevoiusYearlyRecord'] ?? {}),
      approved: data['approved'],
      password: data['password'],
      archived: data['archived'],
    );
  }

  // Convert this UserModel to a Firestore-compatible map
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'name': name,
      'cpfNo': cpfNo,
      'districtoffice': districtoffice,
      'email': email,
      'phonenumber': phoneNo,
      'settlement': settlement,
      'totalSubs': totalSubs,
      'totalSubsInt': totalSubsInt,
      'ltLoansDue': ltLoansDue,
      'stLoansDue': stLoansDue,
      'totalLtLoans': totalLtLoans,
      'totalStLoans': totalStLoans,
      'totalLtIntPaid': totalLtIntPaid,
      'totalStIntPaid': totalStIntPaid,
      'totalDivident': totalDivident,
      'totalShares': totalShares,
      'registrationDate': registrationDate != null
          ? Timestamp.fromDate(registrationDate!)
          : null,
      'employeeNo': employeeNo,
      'currentAddress': currentAddress,
      'permanentAddress': permanentAddress,
      'documents': documents,
      'userPrevoiusMonthlyRecord': userPrevoiusMonthlyRecord,
      'userPrevoiusYearlyRecord': userPrevoiusYearlyRecord,
      'approved': approved,
      'password': password,
      'archived': archived
    };
  }
}
