import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/models/poll_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';

class PollsHistoryPage extends StatefulWidget {
  const PollsHistoryPage({super.key});

  @override
  State<PollsHistoryPage> createState() => _PollsHistoryPageState();
}

class _PollsHistoryPageState extends State<PollsHistoryPage> {
  final CollectionReference pollsRef =
      FirebaseFirestore.instance.collection('polls');

  // Utility: split list into chunks of max chunkSize
  List<List<T>> _chunkList<T>(List<T> list, int chunkSize) {
    List<List<T>> chunks = [];
    for (var i = 0; i < list.length; i += chunkSize) {
      chunks.add(list.sublist(
          i, i + chunkSize > list.length ? list.length : i + chunkSize));
    }
    return chunks;
  }

  Future<List<UserModel>> _fetchUsersByCpfChunks(
      List<dynamic> userDocIds) async {
    List<UserModel> users = [];
    final List<List<dynamic>> chunks =
        _chunkList(userDocIds, 10); // Firestore max 10 in 'whereIn'
    for (var chunk in chunks) {
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('Users')
          .where(FieldPath.documentId, whereIn: chunk)
          .get();
      users.addAll(snapshot.docs.map((doc) => UserModel.fromSnap(doc)));
    }
    return users;
  }

  Future<void> _togglePollActive(PollModel poll, bool isActive) async {
    try {
      await pollsRef.doc(poll.id).update({'isActive': isActive});
      showCtcAppSnackBar(context,
          'Poll "${poll.question}" is now ${isActive ? 'active' : 'inactive'}');
    } catch (e) {
      showCtcAppSnackBar(context, 'Failed to update poll status: $e');
    }
  }

  Future<void> _showDeleteConfirmationDialog(PollModel poll) async {
    bool isDeleting = false;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: const Text('Delete Poll'),
            content: Text(
                'Are you sure you want to delete the poll:\n"${poll.question}"?'),
            actions: [
              TextButton(
                onPressed:
                    isDeleting ? null : () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: isDeleting
                    ? null
                    : () async {
                        setState(() => isDeleting = true);
                        try {
                          await pollsRef.doc(poll.id).delete();
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Poll deleted successfully')),
                          );
                        } catch (e) {
                          setState(() => isDeleting = false);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text('Failed to delete poll: $e')),
                          );
                        }
                      },
                child: isDeleting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                            color: Colors.white, strokeWidth: 2),
                      )
                    : const Text('Yes'),
              ),
            ],
          );
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: StreamBuilder<QuerySnapshot>(
        stream: pollsRef.snapshots(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Center(
                child: Text('Error loading polls: ${snapshot.error}'));
          }
          if (!snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          }
          final polls = snapshot.data!.docs
              .map((doc) => PollModel.fromSnap(doc))
              .toList();
          if (polls.isEmpty) {
            return const Center(child: Text('No polls found.'));
          }
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: polls.length,
            itemBuilder: (context, index) {
              final poll = polls[index];

              // Aggregate user entered "Others" strings and counts
              Map<String, int> aggregateOthersTexts = {};
              if (poll.userVotes != null) {
                for (var vote in poll.userVotes!.values) {
                  if (vote is String && !poll.options.contains(vote)) {
                    aggregateOthersTexts[vote] =
                        (aggregateOthersTexts[vote] ?? 0) + 1;
                  } else if (vote is List) {
                    for (var v in vote) {
                      if (v is String && !poll.options.contains(v)) {
                        aggregateOthersTexts[v] =
                            (aggregateOthersTexts[v] ?? 0) + 1;
                      }
                    }
                  }
                }
              }

              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Poll question and delete button row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            poll.question,
                            style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87),
                          ),
                        ),
                        IconButton(
                          onPressed: () => _showDeleteConfirmationDialog(poll),
                          icon:
                              const Icon(Icons.delete, color: Colors.redAccent),
                          tooltip: 'Delete Poll',
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Poll options and vote counts
                    ...poll.options.map((option) {
                      final count = poll.votes?[option] ?? 0;
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                child: Text(option,
                                    style: const TextStyle(fontSize: 16))),
                            Text('$count votes',
                                style: TextStyle(color: Colors.grey.shade700)),
                          ],
                        ),
                      );
                    }),

                    // Other responses
                    if (aggregateOthersTexts.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      const Text('Other Responses:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      ...aggregateOthersTexts.entries.map((entry) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    entry.key,
                                    style: const TextStyle(
                                        fontSize: 15,
                                        fontStyle: FontStyle.italic),
                                  ),
                                ),
                              ],
                            ),
                          )),
                    ],

                    const SizedBox(height: 12),

                    // Active switch and expiry date, multiple answers
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Active:',
                            style: TextStyle(
                                fontWeight: FontWeight.w600, fontSize: 16)),
                        Switch(
                            value: poll.isActive,
                            onChanged: (val) => _togglePollActive(poll, val)),
                      ],
                    ),
                    if (poll.expiryDate != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 10),
                        child: Text(
                          'Expiry Date: ${poll.expiryDate!.toLocal().toString().split(' ')[0]}',
                          style: TextStyle(color: Colors.grey.shade700),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                          'Allows Multiple Answers: ${poll.allowsMultipleAnswers ? "Yes" : "No"}',
                          style: TextStyle(color: Colors.grey.shade700)),
                    ),

                    // Expand to show voters
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: ExpansionTile(
                        title: const Text('Voters'),
                        children: [
                          SizedBox(
                            height: 300,
                            child: FutureBuilder<List<UserModel>>(
                              future: (poll.userVotes == null ||
                                      poll.userVotes!.isEmpty)
                                  ? Future.value([])
                                  : _fetchUsersByCpfChunks(
                                      poll.userVotes!.keys.toList()),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return const Center(
                                      child: CircularProgressIndicator());
                                }
                                if (!snapshot.hasData ||
                                    snapshot.data!.isEmpty) {
                                  return const Center(
                                      child: Text('No voters found.'));
                                }
                                final pollVoters = snapshot.data!;
                                return ListView.builder(
                                  itemCount: pollVoters.length,
                                  itemBuilder: (context, index) {
                                    final user = pollVoters[index];
                                    final userVote =
                                        poll.userVotes?[user.docId] ??
                                            'No vote';
                                    return ListTile(
                                      title: Text(user.name),
                                      subtitle: Text(
                                          'CPF: ${user.cpfNo}, Voted: $userVote'),
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
