import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/excel_import.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  bool isLoading = false;

  final NumberFormat numberFormat = NumberFormat.decimalPattern('en_IN');

  int customYear = 2025;
  int customMonth = 9;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      num totalMembers =
          ctrl.users.where((element) => element.archived == false).length;

      final membersWithActiveLoans = ctrl.loan
          .where((element) => element.rejectionDate == null)
          .where((element) => element.isSettled == false)
          .where((element) => element.isNew == false)
          .map((e) => e.uid)
          .toSet()
          .length;

      num ltactiveloans = ctrl.loan
          .where((element) => element.loanType == LoanTypes.longTerm)
          .where((element) => element.rejectionDate == null)
          .where((element) => element.isSettled == false)
          .where((element) => element.isNew == false)
          .length;

      num stactiveloans = ctrl.loan
          .where((element) => element.loanType == LoanTypes.emergencyLoan)
          .where((element) => element.rejectionDate == null)
          .where((element) => element.isSettled == false)
          .where((element) => element.isNew == false)
          .length;

      final totalShares = ctrl.users
          .where((element) => element.archived == false)
          .where((element) => element.totalShares != 0)
          .fold<double>(0, (sum, element) => sum + (element.totalShares ?? 0));

      // final totalSubs = ctrl.users
      //     .where((element) => element.archived == false)
      //     .where((element) => element.totalSubs != 0)
      //     .fold<double>(0, (sum, element) => sum + (element.totalSubs ?? 0));

      final totalSubs = ctrl.usermonthly
          .where((element) =>
              element.selectedyear ==
                  TheFinancialYear.getCurrentYearForDatabase() &&
              element.selectedmonth == DateTime.now().month)
          .fold<double>(
              0, (sum, element) => sum + (element.subscriptionPaid ?? 0));

      return SingleChildScrollView(
        padding: const EdgeInsets.only(
          top: 50,
          left: 40,
          right: 40,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // isLoading
            //     ? CircularProgressIndicator()
            //     : ElevatedButton(
            //         onPressed: () async {
            //           setState(() => isLoading = true);
            //           try {
            //             FilePickerResult? result =
            //                 await FilePicker.platform.pickFiles(
            //               type: FileType.custom,
            //               allowedExtensions: ['xlsx'],
            //             );
            //             if (result != null &&
            //                 result.files.single.bytes != null) {
            //               // print("result : $result");
            //               print("result : ${result.files.single.extension}");
            //               print("result : ${result.files.single.name}");
            //               final bytes = result.files.single.bytes!;
            //               // print("bytes : ${bytes.length}");
            //               final excel = Excel.decodeBytes(bytes);
            //               print("excel : $excel");
            //               const String targetSheetName = 'Sheet1(6)';

            //               if (!excel.tables.containsKey(targetSheetName)) {
            //                 print(
            //                     "Sheet '$targetSheetName' not found in Excel file.");
            //                 return;
            //               }

            //               final sheet = excel[targetSheetName];

            //               if (sheet.rows.isEmpty) {
            //                 print(
            //                     "Sheet '$targetSheetName' not found or empty.");
            //                 return;
            //               }

            //               final List<List<dynamic>> cleanedRows = [];
            //               for (int i = 2; i < sheet.rows.length; i++) {
            //                 final row = sheet.rows[i];

            //                 final isEmpty = row.every((cell) {
            //                   final value = cell is Data
            //                       ? (cell.value is TextCellValue
            //                           ? (cell.value as TextCellValue).value
            //                           : cell.value)
            //                       : null;
            //                   return value == null ||
            //                       value.toString().trim().isEmpty;
            //                 });
            //                 if (isEmpty) {
            //                   continue;
            //                 }

            //                 final cpfCell = row.length > 3 ? row[3] : null;
            //                 dynamic cpfValue;
            //                 if (cpfCell is Data) {
            //                   cpfValue = cpfCell.value is TextCellValue
            //                       ? (cpfCell.value as TextCellValue).value
            //                       : cpfCell.value;
            //                 }
            //                 final cpfNo = cpfValue is num
            //                     ? cpfValue
            //                     : num.tryParse(
            //                         cpfValue?.toString().trim() ?? '');
            //                 if (cpfNo == null || cpfNo == 0) {
            //                   print(
            //                       "Skipped row $i: Invalid CPF No – raw value: '$cpfValue'");
            //                   continue;
            //                 }

            //                 final cleanedRow = row.map((cell) {
            //                   final val = cell is Data
            //                       ? (cell.value is TextCellValue
            //                           ? (cell.value as TextCellValue).value
            //                           : cell.value)
            //                       : null;
            //                   return val ?? '';
            //                 }).toList();
            //                 cleanedRows.add(cleanedRow);
            //               }

            //               if (cleanedRows.isNotEmpty) {
            //                 await importExcelData(
            //                   customYear: customYear,
            //                   cleanedRows: cleanedRows,
            //                   customMonth: customMonth,
            //                 );
            //               } else {
            //                 print(
            //                     "No valid rows found to import in sheet: $targetSheetName");
            //               }

            //               for (final table in excel.tables.keys) {
            //                 final sheet = excel.tables[table];
            //                 if (sheet == null || sheet.rows.isEmpty) {
            //                   continue;
            //                 }
            //                 final List<List<dynamic>> cleanedRows = [];
            //                 for (int i = 2; i < sheet.rows.length; i++) {
            //                   final row = sheet.rows[i];
            //                   final isEmpty = row.every((cell) {
            //                     final value = cell is Data
            //                         ? (cell.value is TextCellValue
            //                             ? (cell.value as TextCellValue).value
            //                             : cell.value)
            //                         : null;
            //                     return value == null ||
            //                         value.toString().trim().isEmpty;
            //                   });
            //                   if (isEmpty) {
            //                     continue;
            //                   }
            //                   final cpfCell = row.length > 2 ? row[2] : null;
            //                   dynamic cpfValue;
            //                   if (cpfCell is Data) {
            //                     cpfValue = cpfCell.value is TextCellValue
            //                         ? (cpfCell.value as TextCellValue).value
            //                         : cpfCell.value;
            //                   }
            //                   final cpfNo = cpfValue is num
            //                       ? cpfValue
            //                       : num.tryParse(
            //                           cpfValue?.toString().trim() ?? '');
            //                   if (cpfNo == null || cpfNo == 0) {
            //                     print(
            //                         " Skipped row $i: Invalid CPF No – raw value: '$cpfValue'");
            //                     continue;
            //                   }
            //                   //  3. Row is valid → Clean it
            //                   final cleanedRow = row.map((cell) {
            //                     final val = cell is Data
            //                         ? (cell.value is TextCellValue
            //                             ? (cell.value as TextCellValue).value
            //                             : cell.value)
            //                         : null;
            //                     return val ?? '';
            //                   }).toList();
            //                   cleanedRows.add(cleanedRow);
            //                 }
            //                 if (cleanedRows.isNotEmpty) {
            //                   await importExcelData(
            //                     customYear: customYear,
            //                     cleanedRows: cleanedRows,
            //                     customMonth: customMonth,
            //                   );
            //                 } else {
            //                   print(
            //                       "No valid rows found to import in sheet: $table");
            //                 }
            //               }
            //             }
            //           } catch (e) {
            //             debugPrint("Error during import: ${e.toString()}");
            //           } finally {
            //             setState(() => isLoading = false);
            //           }
            //         },
            //         child: const Text("Import"),
            //       ),
            Text(
              "DASHBOARD",
              style: const TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(DateFormat('dd/MM/yyyy').format(DateTime.now()),
                style:
                    const TextStyle(fontSize: 22, fontWeight: FontWeight.w500)),
            SizedBox(height: 30),
            StaggeredGrid.extent(
                crossAxisSpacing: 20,
                mainAxisSpacing: 20,
                maxCrossAxisExtent: 300,
                children: [
                  DbCustomCard(
                    iconn: CupertinoIcons.person_2,
                    texxt: "Total Members",
                    dataTexxt: totalMembers.toString(),
                    color: const Color(0xffd8f0fc),
                  ),
                  DbCustomCard(
                    iconn: CupertinoIcons.person_2,
                    texxt: "Members with active loans",
                    dataTexxt: membersWithActiveLoans.toString(),
                    color: const Color(0xffd2f8e2),
                  ),
                  DbCustomCard(
                    iconn: CupertinoIcons.xmark_seal,
                    texxt: "Active Lt Loans",
                    dataTexxt: ltactiveloans.toString(),
                    color: const Color(0xffd8f0fc),
                  ),
                  DbCustomCard(
                    iconn: CupertinoIcons.xmark_seal,
                    texxt: "Active St Loans",
                    dataTexxt: stactiveloans.toString(),
                    color: const Color(0xffd2f8e2),
                  ),
                  DbCustomCard(
                    iconn: CupertinoIcons.line_horizontal_3_decrease,
                    texxt: "Share Value Hold",
                    dataTexxt: numberFormat.format(totalShares),
                    // dataTexxt: '-',
                    color: const Color(0xffd2f8e2),
                  ),
                  DbCustomCard(
                    iconn: Icons.money,
                    texxt: "Monthly Collection Value",
                    // dataTexxt: '-',
                    dataTexxt: numberFormat.format(totalSubs),
                    color: const Color(0xffd8f0fc),
                  ),
                  DbCustomCard(
                    iconn: CupertinoIcons.xmark_seal,
                    texxt: "Available Balance",
                    dataTexxt: "-",
                    color: const Color(0xffd2f8e2),
                  ),
                  DbCustomCard(
                    iconn: CupertinoIcons.plus,
                    texxt: "Total Society Balance",
                    dataTexxt: "-",
                    color: const Color(0xffd8f0fc),
                  ),
                  DbCustomCard(
                    iconn: CupertinoIcons.sort_up,
                    texxt: "Total Subscription",
                    dataTexxt: "-",
                    color: const Color(0xffd2f8e2),
                  ),
                  DbCustomCard(
                    iconn: Icons.account_balance_outlined,
                    texxt: "Total Bank Balance",
                    dataTexxt: "-",
                    color: const Color(0xffd8f0fc),
                  ),
                  DbCustomCard(
                    iconn: Icons.attach_money_sharp,
                    texxt: "Total Cash Balance",
                    dataTexxt: ctrl.cashbalance.toString(),
                    color: const Color(0xffd2f8e2),
                  ),
                ])
          ],
        ),
      );
    });
  }
}

// Update required – version not supported -- db
