// ignore_for_file: use_build_context_synchronously, deprecated_member_use, avoid_web_libraries_in_flutter

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/subsidiary_ledger_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:typed_data';
import 'package:pluto_grid/pluto_grid.dart';

class SubsidiaryLedgerPage extends StatefulWidget {
  const SubsidiaryLedgerPage({super.key});

  @override
  State<SubsidiaryLedgerPage> createState() => _SubsidiaryLedgerPageState();
}

final num startMonth = 4;

final List<PlutoColumnGroup> columnGroups = [
  PlutoColumnGroup(
    title: 'PLACE OF POSTING',
    fields: ['district_office'],
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumnGroup(
    title: 'OPENING BALANCE OF LOAN',
    fields: ['ob_subscription', 'ob_lt', 'ob_st', 'ob_shares'],
  ),
  ...getDynamicMonthNumbers().map((monthNum) {
    final prefix = monthShortName(monthNum);
    final isColored = monthNum % 2 == 0;
    return PlutoColumnGroup(
      title: prefix,
      fields: [
        'subs_$monthNum',
        'lt_installment_$monthNum',
        'st_installment_$monthNum',
        'interest_$monthNum',
        'penalty_$monthNum',
      ],
      backgroundColor: isColored ? Color(0xffC9E9D2) : null,
    );
  }),
];

final List<PlutoColumn> columns = [
  PlutoColumn(
    title: 'SR. NO',
    field: 'sr_no',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'CPF NO.',
    field: 'cpf_no',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'NAME OF MEMBER',
    field: 'name',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'DISTRICT OFFICE',
    field: 'district_office',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'OB SUBSCRIPTION',
    field: 'ob_subscription',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  PlutoColumn(
    title: 'OB LONG TERM',
    field: 'ob_lt',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  PlutoColumn(
    title: 'OB SHORT TERM',
    field: 'ob_st',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  PlutoColumn(
    title: 'OB SHARES',
    field: 'ob_shares',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  ...getDynamicMonthNumbers().expand((monthNum) {
    final prefix = monthShortName(monthNum);
    final isColored = monthNum % 2 == 0;
    return [
      PlutoColumn(
        title: '$prefix SUBSCRIPTION',
        field: 'subs_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix LT INSTALLMENT',
        field: 'lt_installment_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix ST INSTALLMENT',
        field: 'st_installment_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix INTEREST',
        field: 'interest_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix PENALTY',
        field: 'penalty_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
    ];
  })
];

class _SubsidiaryLedgerPageState extends State<SubsidiaryLedgerPage> {
  List<MonthlyLedgerModel> ledgerList = [];

  String? subLedgerSelectedFinancialYear;
  int? subLedgerSelectedYear; // Keep for backward compatibility

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  bool slIsLoading = false;
  bool slCSVLoading = false;
  bool slPDFLoading = false;

  List<PlutoRow> rows = [];

  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    subLedgerSelectedFinancialYear = TheFinancialYear.getCurrentFinancialYear();
    subLedgerSelectedYear = TheFinancialYear.getCurrentFinancialYearStartYear();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: subLedgerSelectedYear,
                        decoration: InputDecoration(
                            hintText: "Select Year",
                            constraints: const BoxConstraints(
                                maxWidth: 150, maxHeight: 45),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5))),
                        items: List.generate(
                          yearList.length,
                          (index) {
                            return DropdownMenuItem(
                              value: yearList[index],
                              child: Text(yearList[index].toString()),
                            );
                          },
                        ),
                        onChanged: (value) async {
                          setState(() => subLedgerSelectedYear = value);

                          stateManager.removeAllRows();

                          await checkAndApplyData(ctrl);

                          final List<UserModel> sortedUsers =
                              List<UserModel>.from(ctrl.users)
                                ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

                          List<PlutoRow> newRows = [];

                          for (final entry in sortedUsers.asMap().entries) {
                            final index = entry.key;
                            final user = entry.value;

                            final doName = ctrl.districtoffice.firstWhereOrNull(
                              (element) => element.docId == user.districtoffice,
                            );

                            final Map<int, dynamic> monthlyData = {};
                            for (int i = 0; i < 12; i++) {
                              final monthNum = getMonthNum(i);
                              final match = ctrl.usermonthly.firstWhereOrNull(
                                (e) =>
                                    e.cpfNo == user.cpfNo &&
                                    e.selectedmonth == monthNum &&
                                    e.selectedyear == subLedgerSelectedYear,
                              );
                              monthlyData[monthNum] = match;
                            }

                            if (monthlyData.values
                                .every((val) => val == null)) {
                              continue;
                            }

                            final Map<String, PlutoCell> monthCells = {};
                            for (int i = 0; i < 12; i++) {
                              final monthNum = getMonthNum(i);
                              final data = monthlyData[monthNum];

                              monthCells.addAll({
                                'subs_$monthNum':
                                    PlutoCell(value: data?.subs ?? ''),
                                'lt_installment_$monthNum':
                                    PlutoCell(value: data?.ltInstallment ?? ''),
                                'st_installment_$monthNum':
                                    PlutoCell(value: data?.stInstallment ?? ''),
                                'interest_$monthNum':
                                    PlutoCell(value: data?.interest ?? ''),
                                'penalty_$monthNum':
                                    PlutoCell(value: data?.penalty ?? ''),
                              });
                            }

                            newRows.add(PlutoRow(cells: {
                              'sr_no': PlutoCell(value: index + 1),
                              'cpf_no': PlutoCell(value: user.cpfNo),
                              'name': PlutoCell(value: user.name.toUpperCase()),
                              'district_office':
                                  PlutoCell(value: doName?.name ?? ""),
                              'ob_subscription': PlutoCell(value: ''),
                              'ob_lt': PlutoCell(value: ''),
                              'ob_st': PlutoCell(value: ''),
                              'ob_shares': PlutoCell(value: ''),
                              ...monthCells,
                            }));
                          }

                          if (newRows.isNotEmpty) {
                            final Map<String, double> totalMap = {};

                            for (final row in newRows) {
                              row.cells.forEach((key, cell) {
                                if (key.startsWith('subs_') ||
                                    key.startsWith('lt_installment_') ||
                                    key.startsWith('st_installment_') ||
                                    key.startsWith('interest_') ||
                                    key.startsWith('penalty_')) {
                                  final val =
                                      double.tryParse(cell.value.toString()) ??
                                          0.0;
                                  totalMap[key] = (totalMap[key] ?? 0.0) + val;
                                }
                              });
                            }

                            final totalRow = PlutoRow(cells: {
                              'sr_no': PlutoCell(value: ''),
                              'cpf_no': PlutoCell(value: ''),
                              'name': PlutoCell(value: 'TOTAL'),
                              'district_office': PlutoCell(value: ''),
                              'ob_subscription': PlutoCell(value: ''),
                              'ob_lt': PlutoCell(value: ''),
                              'ob_st': PlutoCell(value: ''),
                              'ob_shares': PlutoCell(value: ''),
                              ...generateTotalCells(totalMap),
                            });

                            newRows.add(totalRow);
                          }

                          rows = newRows;
                          stateManager.appendRows(rows);

                          setState(() {
                            slIsLoading = false;
                          });
                        })),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomHeaderButton(
                      onPressed: () async => exportToCSV(),
                      buttonName: 'Export to CSV',
                    ),
                    SizedBox(width: 5),
                    CustomHeaderButton(
                      onPressed: () async => await exportToPDF(),
                      buttonName: 'Export to PDF',
                    ),
                    SizedBox(width: 5),
                    slIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => await slOnSave(ctrl),
                            buttonName: 'SAVE')
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "SUBSIDIARY LEDGER FOR THE YEAR $subLedgerSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columnGroups: columnGroups,
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);

                    final ctrl = Get.find<HomeCtrl>();
                    final newRows = generateSubsidiaryLedgerRows(
                      ctrl.users,
                      ctrl,
                      subLedgerSelectedYear,
                    );

                    rows = newRows;
                    stateManager.appendRows(rows);
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {
                    final field = event.column.field;

                    if (field == 'ob_subscription' ||
                        field == 'ob_lt' ||
                        field == 'ob_st' ||
                        field == 'ob_shares') {
                      // Editable opening balance fields logic here if needed
                    }
                  },
                  configuration: const PlutoGridConfiguration(
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> slOnSave(HomeCtrl ctrl) async {
    setState(() => slIsLoading = true);
    if (stateManager.rows.isEmpty) {
      showCtcAppSnackBar(context, "No Data to Save");
      setState(() => slIsLoading = false);
      return;
    }
    try {
      final fbSubsidiaryLedgerSnap = await FBFireStore.subsidiaryledger
          .where("year", isEqualTo: subLedgerSelectedYear)
          .get();

      final snapshot = fbSubsidiaryLedgerSnap;

      if (snapshot.docs.isNotEmpty) {
        final existingDoc = snapshot.docs.first;
        final mainSubLedRef = FBFireStore.subsidiaryledger.doc(existingDoc.id);

        await mainSubLedRef.update({'updatedAt': DateTime.now()});

        final userList = ctrl.users;
        for (var user in userList) {
          final monthlyEntriesMap = List.generate(12, (i) {
            final monthNum = getMonthNum(i);

            final monthlyData = ctrl.usermonthly.firstWhereOrNull(
              (e) =>
                  e.cpfNo == user.cpfNo &&
                  e.selectedmonth == monthNum &&
                  e.selectedyear == subLedgerSelectedYear,
            );

            final userRows = rows.where(
              (r) => r.cells['cpf_no']?.value == user.cpfNo,
            );
            final userObRow = rows.firstWhereOrNull(
                (r) => r.cells['cpf_no']?.value == user.cpfNo);

            double subsTotal = 0;
            double ltTotal = 0;
            double stTotal = 0;
            double interestTotal = 0;
            double penaltyTotal = 0;

            for (final row in userRows) {
              final subs = double.tryParse(
                      row.cells['subs_$monthNum']?.value.toString() ?? '0') ??
                  0;
              final ltInst = double.tryParse(
                      row.cells['lt_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final stInst = double.tryParse(
                      row.cells['st_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final interest = double.tryParse(
                      row.cells['interest_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final penalty = double.tryParse(
                      row.cells['penalty_$monthNum']?.value.toString() ??
                          '0') ??
                  0;

              subsTotal += subs;
              ltTotal += ltInst;
              stTotal += stInst;
              interestTotal += interest;
              penaltyTotal += penalty;
            }

            return MonthlyLedgerModel(
              month: monthNum.toString(),
              subscription: monthlyData?.subs ?? 0,
              longTermInstallment: monthlyData?.ltInstallment ?? 0,
              shortTermInstallment: monthlyData?.stInstallment ?? 0,
              interest: monthlyData?.interest ?? 0,
              penalty: monthlyData?.penalty ?? 0,
              totalinterest: interestTotal,
              totallongTermInstallment: ltTotal,
              totalshortTermInstallment: stTotal,
              totalsubscription: subsTotal,
              totalPenalty: penaltyTotal,
              obSubs: user.obSubs ?? 0,

              // userObRow != null
              //     ? num.tryParse(userObRow.cells['ob_subscription']?.value
              //                 .toString() ??
              //             "0") ??
              //         0
              //     : 0,
              obShares: user.obShares ?? 0,

              // userObRow != null
              //     ? num.tryParse(
              //             userObRow.cells['ob_shares']?.value.toString() ??
              //                 "0") ??
              //         0
              //     : 0,
              obst: user.obSt ?? 0,
              // userObRow != null
              //     ? num.tryParse(
              //             userObRow.cells['ob_st']?.value.toString() ?? "0") ??
              //         0
              //     : 0,
              oblt: user.obLt ?? 0,
              // userObRow != null
              //     ? num.tryParse(
              //             userObRow.cells['ob_lt']?.value.toString() ?? "0") ??
              //         0
              //     : 0,
            ).toJson();
          });

          await mainSubLedRef.collection("userData").doc(user.docId).set({
            'uId': user.docId,
            'cpfNo': user.cpfNo,
            'name': user.name,
            'districtOffice': user.districtoffice,
            'monthlyEntries': monthlyEntriesMap,
          }, SetOptions(merge: true));
        }

        showCtcAppSnackBar(context, "Subsidiary Ledger Updated Successfully");
      } else {
        final userList = ctrl.users;
        final mainSubLedRef = FBFireStore.subsidiaryledger.doc();

        await mainSubLedRef.set({
          'year': subLedgerSelectedYear ??
              TheFinancialYear.getCurrentYearForDatabase(),
          'updatedAt': null,
        });

        for (var user in userList) {
          final monthlyEntriesMap = List.generate(12, (i) {
            final monthNum = getMonthNum(i);

            final monthlyData = ctrl.usermonthly.firstWhereOrNull(
              (e) =>
                  e.cpfNo == user.cpfNo &&
                  e.selectedmonth == monthNum &&
                  e.selectedyear == subLedgerSelectedYear,
            );

            final userRows =
                rows.where((r) => r.cells['cpf_no']?.value == user.cpfNo);
            final userObRow = rows.firstWhereOrNull(
                (r) => r.cells['cpf_no']?.value == user.cpfNo);

            double subsTotal = 0;
            double ltTotal = 0;
            double stTotal = 0;
            double interestTotal = 0;
            double penaltyTotal = 0;

            for (final row in userRows) {
              final subs = double.tryParse(
                      row.cells['subs_$monthNum']?.value.toString() ?? '0') ??
                  0;
              final ltInst = double.tryParse(
                      row.cells['lt_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final stInst = double.tryParse(
                      row.cells['st_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final interest = double.tryParse(
                      row.cells['interest_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final penalty = double.tryParse(
                      row.cells['penalty_$monthNum']?.value.toString() ??
                          '0') ??
                  0;

              subsTotal += subs;
              ltTotal += ltInst;
              stTotal += stInst;
              interestTotal += interest;
              penaltyTotal += penalty;
            }

            return MonthlyLedgerModel(
              month: monthNum.toString(),
              subscription: monthlyData?.subs ?? 0,
              longTermInstallment: monthlyData?.ltInstallment ?? 0,
              shortTermInstallment: monthlyData?.stInstallment ?? 0,
              interest: monthlyData?.interest ?? 0,
              penalty: monthlyData?.penalty ?? 0,
              totalinterest: interestTotal,
              totallongTermInstallment: ltTotal,
              totalshortTermInstallment: stTotal,
              totalsubscription: subsTotal,
              totalPenalty: penaltyTotal,
              obSubs: userObRow != null
                  ? num.tryParse(userObRow.cells['ob_subscription']?.value
                              .toString() ??
                          "0") ??
                      0
                  : 0,
              obShares: userObRow != null
                  ? num.tryParse(
                          userObRow.cells['ob_shares']?.value.toString() ??
                              "0") ??
                      0
                  : 0,
              obst: userObRow != null
                  ? num.tryParse(
                          userObRow.cells['ob_st']?.value.toString() ?? "0") ??
                      0
                  : 0,
              oblt: userObRow != null
                  ? num.tryParse(
                          userObRow.cells['ob_lt']?.value.toString() ?? "0") ??
                      0
                  : 0,
            ).toJson();
          });

          await FBFireStore.subsidiaryledger
              .doc(mainSubLedRef.id)
              .collection("userData")
              .doc(user.docId)
              .set({
            'uId': user.docId,
            'cpfNo': user.cpfNo,
            'name': user.name,
            'districtOffice': user.districtoffice,
            'monthlyEntries': monthlyEntriesMap,
          });
        }

        showCtcAppSnackBar(context, "Subsidiary Ledger Saved Successfuly");
      }
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(
          context, "Error occurred while saving subsidiary ledger.");
    }

    setState(() => slIsLoading = false);
  }

  Future<void> checkAndApplyData(HomeCtrl ctrl) async {
    try {
      setState(() {
        ledgerList = [];
      });

      final fbSubsidiaryLedgerSnap = await FBFireStore.subsidiaryledger
          .where("year", isEqualTo: subLedgerSelectedYear)
          .get();

      if (fbSubsidiaryLedgerSnap.size == 1) {
        final userDataSnap = await FBFireStore.subsidiaryledger
            .doc(fbSubsidiaryLedgerSnap.docs.first.id)
            .collection("userData")
            .get();

        List<MonthlyLedgerModel> templedgerList = userDataSnap.docs
            .map((doc) {
              final uId = doc.id;
              final data = doc.data();

              final matchedUser =
                  ctrl.users.firstWhereOrNull((user) => user.docId == uId);
              if (matchedUser == null) {
                return <MonthlyLedgerModel>[];
              }

              return List.generate(12, (i) {
                final monthNum = getMonthNum(i);
                final List monthlyEntries = data['monthlyEntries'] ?? [];

                final entryForMonth =
                    (monthlyEntries).cast<Map<String, dynamic>>().firstWhere(
                          (entry) => entry["month"] == monthNum.toString(),
                          orElse: () => <String, dynamic>{},
                        );

                if (entryForMonth.isEmpty) {
                  return MonthlyLedgerModel(
                    month: monthNum.toString(),
                    subscription: 0,
                    longTermInstallment: 0,
                    shortTermInstallment: 0,
                    interest: 0,
                    totalinterest: 0,
                    totallongTermInstallment: 0,
                    totalshortTermInstallment: 0,
                    totalsubscription: 0,
                    penalty: 0,
                    totalPenalty: 0,
                    obSubs: rows.isNotEmpty
                        ? num.tryParse(
                                rows.first.cells['ob_subscription']?.value) ??
                            0
                        : 0,
                    obShares: rows.isNotEmpty
                        ? num.tryParse(rows.first.cells['ob_shares']?.value) ??
                            0
                        : 0,
                    obst: rows.isNotEmpty
                        ? num.tryParse(rows.first.cells['ob_st']?.value) ?? 0
                        : 0,
                    oblt: rows.isNotEmpty
                        ? num.tryParse(rows.first.cells['ob_lt']?.value) ?? 0
                        : 0,
                  );
                }

                return MonthlyLedgerModel(
                  month: monthNum.toString(),
                  subscription: (entryForMonth["subscription"] ?? 0),
                  longTermInstallment:
                      (entryForMonth["longTermInstallment"] ?? 0),
                  shortTermInstallment:
                      (entryForMonth["shortTermInstallment"] ?? 0),
                  interest: (entryForMonth["interest"] ?? 0),
                  penalty: (entryForMonth["penalty"] ?? 0),
                  totalinterest: (entryForMonth["totalinterest"] ?? 0),
                  totallongTermInstallment:
                      (entryForMonth["totallongTermInstallment"] ?? 0),
                  totalshortTermInstallment:
                      (entryForMonth["totalshortTermInstallment"] ?? 0),
                  totalsubscription: (entryForMonth["totalsubscription"] ?? 0),
                  totalPenalty: (entryForMonth["totalPenalty"] ?? 0),
                  obSubs: rows.isNotEmpty
                      ? num.tryParse(
                              rows.first.cells['ob_subscription']?.value) ??
                          0
                      : 0,
                  obShares: rows.isNotEmpty
                      ? num.tryParse(rows.first.cells['ob_shares']?.value) ?? 0
                      : 0,
                  obst: rows.isNotEmpty
                      ? num.tryParse(rows.first.cells['ob_st']?.value) ?? 0
                      : 0,
                  oblt: rows.isNotEmpty
                      ? num.tryParse(rows.first.cells['ob_lt']?.value) ?? 0
                      : 0,
                );
              });
            })
            .expand((e) => e)
            .toList();

        setState(() {
          ledgerList = templedgerList;
        });
      } else if (fbSubsidiaryLedgerSnap.docs.isEmpty) {
        rows.clear();

        final sortedUsers = List<UserModel>.from(ctrl.users)
          ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

        final emptyLedgerList = sortedUsers.expand((user) {
          return List.generate(12, (i) {
            final monthNum = getMonthNum(i);

            return MonthlyLedgerModel(
              month: monthNum.toString(),
              subscription: 0,
              longTermInstallment: 0,
              shortTermInstallment: 0,
              interest: 0,
              totalinterest: 0,
              totallongTermInstallment: 0,
              totalshortTermInstallment: 0,
              totalsubscription: 0,
              penalty: 0,
              totalPenalty: 0,
              obSubs: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_subscription']?.value) ??
                      0
                  : 0,
              obShares: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_shares']?.value) ?? 0
                  : 0,
              obst: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_st']?.value) ?? 0
                  : 0,
              oblt: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_lt']?.value) ?? 0
                  : 0,
            );
          });
        }).toList();

        setState(() {
          ledgerList = emptyLedgerList;
        });
      }

      if (mounted) setState(() {});
    } catch (e, stack) {}
  }

  Future<void> exportToCSV() async {
    setState(() => slCSVLoading = true);

    try {
      final csv = StringBuffer();

      String monthShortName(int month) {
        const names = [
          '',
          'JAN',
          'FEB',
          'MAR',
          'APR',
          'MAY',
          'JUN',
          'JUL',
          'AUG',
          'SEP',
          'OCT',
          'NOV',
          'DEC'
        ];
        return names[month];
      }

      final headers = [
        'SR. NO',
        'CPF NO.',
        'NAME OF MEMBER',
        'DISTRICT OFFICE',
        'OB SUBSCRIPTION',
        'OB LONG TERM',
        'OB SHORT TERM',
        'OB SHARES',
        ...List.generate(12, (i) {
          final monthNum = getMonthNum(i);
          final m = monthShortName(monthNum);
          return [
            '$m Subscription',
            '$m LT Installment',
            '$m ST Installment',
            '$m Interest',
            '$m Penalty'
          ];
        }).expand((x) => x),
      ];

      csv.writeln(headers.map((e) => '"$e"').join(','));

      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        setState(() => slCSVLoading = false);
        return;
      }

      for (var row in rows) {
        final rowData = [
          row.cells['sr_no']?.value ?? '',
          row.cells['cpf_no']?.value ?? '',
          row.cells['name']?.value ?? '',
          row.cells['district_office']?.value ?? '',
          row.cells['ob_subscription']?.value ?? '',
          row.cells['ob_lt']?.value ?? '',
          row.cells['ob_st']?.value ?? '',
          row.cells['ob_shares']?.value ?? '',
          ...List.generate(12, (i) {
            final monthNum = getMonthNum(i);
            return [
              row.cells['subs_$monthNum']?.value ?? '',
              row.cells['lt_installment_$monthNum']?.value ?? '',
              row.cells['st_installment_$monthNum']?.value ?? '',
              row.cells['interest_$monthNum']?.value ?? '',
              row.cells['penalty_$monthNum']?.value ?? '',
            ];
          }).expand((x) => x),
        ].map((e) => '"$e"');

        csv.writeln(rowData.join(','));
      }

      final csvBytes = html.Blob([csv.toString()]);
      final csvUrl = html.Url.createObjectUrlFromBlob(csvBytes);

      final anchor = html.AnchorElement(href: csvUrl)
        ..setAttribute('download', 'subsidiary_ledger.csv')
        ..click();

      html.Url.revokeObjectUrl(csvUrl);
      setState(() => slCSVLoading = false);
    } catch (e) {
      setState(() => slCSVLoading = false);
      showCtcAppSnackBar(context, "Failed to export CSV: $e");
    } finally {
      setState(() => slCSVLoading = false);
    }
  }

  Future<void> exportToPDF() async {
    setState(() => slPDFLoading = true);

    try {
      final pdf = pw.Document();

      String monthShortName(int month) {
        const names = [
          '',
          'JAN',
          'FEB',
          'MAR',
          'APR',
          'MAY',
          'JUN',
          'JUL',
          'AUG',
          'SEP',
          'OCT',
          'NOV',
          'DEC'
        ];
        return names[month];
      }

      final headers = [
        'SR.NO',
        'CPF NO.',
        'NAME',
        'DISTRICT OFFICE',
        'OB SUB',
        'OB LONG TERM',
        'OB SHORT TERM',
        'OB SHARES',
        ...List.generate(12, (i) {
          final monthNum = getMonthNum(i);
          final prefix = monthShortName(monthNum);
          return [
            '$prefix SUB',
            '$prefix LT INSTALLMENT',
            '$prefix ST INSTALLMENT',
            '$prefix INT',
            '$prefix PENALTY',
          ];
        }).expand((e) => e)
      ];

      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        setState(() => slPDFLoading = false);
        return;
      }

      final data = <List<String>>[
        headers,
        for (var row in rows)
          [
            row.cells['sr_no']?.value.toString() ?? '',
            row.cells['cpf_no']?.value.toString() ?? '',
            row.cells['name']?.value.toString() ?? '',
            row.cells['district_office']?.value.toString() ?? '',
            row.cells['ob_subscription']?.value.toString() ?? '',
            row.cells['ob_lt']?.value.toString() ?? '',
            row.cells['ob_st']?.value.toString() ?? '',
            row.cells['ob_shares']?.value.toString() ?? '',
            ...List.generate(12, (i) {
              final monthNum = getMonthNum(i);
              return [
                row.cells['subs_$monthNum']?.value.toString() ?? '',
                row.cells['lt_installment_$monthNum']?.value.toString() ?? '',
                row.cells['st_installment_$monthNum']?.value.toString() ?? '',
                row.cells['interest_$monthNum']?.value.toString() ?? '',
                row.cells['penalty_$monthNum']?.value.toString() ?? '',
              ];
            }).expand((e) => e),
          ]
      ];

      pdf.addPage(
        pw.MultiPage(
          pageFormat:
              PdfPageFormat(4000, PdfPageFormat.a3.height, marginAll: 40),
          orientation: pw.PageOrientation.landscape,
          margin: pw.EdgeInsets.all(10),
          build: (context) => [
            pw.Text(
              textAlign: pw.TextAlign.center,
              'Subsidiary Ledger Report',
              style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            pw.Table(
              border: pw.TableBorder.all(width: 0.5),
              defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
              children: [
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: PdfColors.grey300),
                  children: headers.map((header) {
                    return pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text(
                        header,
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 7,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    );
                  }).toList(),
                ),
                ...data.sublist(1).map((row) {
                  return pw.TableRow(
                    children: row.map((cell) {
                      return pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text(
                          cell,
                          style: pw.TextStyle(fontSize: 7),
                          textAlign: pw.TextAlign.center,
                          softWrap: false,
                          overflow: pw.TextOverflow.clip,
                        ),
                      );
                    }).toList(),
                  );
                }),
              ],
            ),
          ],
        ),
      );

      final pdfBytes = await pdf.save();
      final blob = html.Blob([Uint8List.fromList(pdfBytes)]);
      final url = html.Url.createObjectUrlFromBlob(blob);

      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'subsidiary_ledger.pdf')
        ..click();

      html.Url.revokeObjectUrl(url);
      setState(() => slPDFLoading = false);
    } catch (e) {
      setState(() => slPDFLoading = false);
      showCtcAppSnackBar(context, "Failed to export PDF: $e");
    } finally {
      setState(() => slPDFLoading = false);
    }
  }

  List<PlutoRow> generateSubsidiaryLedgerRows(
      List<UserModel> users, HomeCtrl ctrl, int? selectedYear) {
    final sortedUsers = List<UserModel>.from(users)
      ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

    final rows = sortedUsers.asMap().entries.map((entry) {
      final index = entry.key;
      final user = entry.value;

      final doName = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == user.districtoffice,
      );

      final Map<int, dynamic> monthlyData = {};

      for (int i = 0; i < 12; i++) {
        final monthNum = getMonthNum(i);
        final match = ctrl.usermonthly.firstWhereOrNull(
          (e) =>
              e.cpfNo == user.cpfNo &&
              e.selectedmonth == monthNum &&
              e.selectedyear == selectedYear,
        );
        monthlyData[monthNum] = match;
      }

      final Map<String, PlutoCell> monthCells = {};
      for (int i = 0; i < 12; i++) {
        final monthNum = getMonthNum(i);
        final data = monthlyData[monthNum];

        monthCells.addAll({
          'subs_$monthNum': PlutoCell(value: formatCeil(data?.subs)),
          'lt_installment_$monthNum':
              PlutoCell(value: formatCeil(data?.ltInstallment)),
          'st_installment_$monthNum':
              PlutoCell(value: formatCeil(data?.stInstallment)),
          'interest_$monthNum': PlutoCell(value: formatCeil(data?.interest)),
          'penalty_$monthNum': PlutoCell(value: formatCeil(data?.penalty)),
        });
      }

      return PlutoRow(
        cells: {
          'sr_no': PlutoCell(value: index + 1),
          'cpf_no': PlutoCell(value: user.cpfNo),
          'name': PlutoCell(value: user.name.toUpperCase()),
          'district_office': PlutoCell(value: doName?.name ?? ""),
          'ob_subscription': PlutoCell(value: user.obSubs),
          'ob_lt': PlutoCell(value: user.obLt),
          'ob_st': PlutoCell(value: user.obSt),
          'ob_shares': PlutoCell(value: user.obShares),
          ...monthCells,
        },
      );
    }).toList();

    final Map<String, double> totalMap = {};
    for (final row in rows) {
      row.cells.forEach((key, cell) {
        if (key.startsWith('subs_') ||
            key.startsWith('lt_installment_') ||
            key.startsWith('st_installment_') ||
            key.startsWith('interest_') ||
            key.startsWith('penalty_')) {
          final val = double.tryParse(cell.value.toString()) ?? 0.0;
          totalMap[key] = (totalMap[key] ?? 0.0) + val;
        }
      });
    }

    final totalRow = PlutoRow(
      cells: {
        'sr_no': PlutoCell(value: ''),
        'cpf_no': PlutoCell(value: ''),
        'name': PlutoCell(value: 'TOTAL'),
        'district_office': PlutoCell(value: ''),
        'ob_subscription': PlutoCell(value: ''),
        'ob_lt': PlutoCell(value: ''),
        'ob_st': PlutoCell(value: ''),
        'ob_shares': PlutoCell(value: ''),
        ...generateTotalCells(totalMap),
      },
    );

    rows.add(totalRow);
    return rows;
  }
}

String formatCeil(dynamic val) {
  if (val == null) return '';
  final parsed = num.tryParse(val.toString());
  return parsed != null ? parsed.ceil().toString() : '';
}

Map<String, PlutoCell> generateTotalCells(Map<String, double> totalMap) {
  final Map<String, PlutoCell> cells = {};
  for (int i = 0; i < 12; i++) {
    final monthNum = getMonthNum(i);
    cells['subs_$monthNum'] =
        PlutoCell(value: totalMap['subs_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['lt_installment_$monthNum'] = PlutoCell(
        value: totalMap['lt_installment_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['st_installment_$monthNum'] = PlutoCell(
        value: totalMap['st_installment_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['interest_$monthNum'] = PlutoCell(
        value: totalMap['interest_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['penalty_$monthNum'] = PlutoCell(
        value: totalMap['penalty_$monthNum']?.toStringAsFixed(2) ?? '');
  }
  return cells;
}

int getMonthNum(int index) => (index + 4) > 12 ? (index - 8) : (index + 4);
