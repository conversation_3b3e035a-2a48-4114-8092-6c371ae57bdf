// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/expenses_model.dart';
import 'package:foodcorp_admin/models/transaction_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html; // For web file download (if Flutter Web)
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';

class CashbookPage extends StatefulWidget {
  const CashbookPage({super.key});

  @override
  State<CashbookPage> createState() => _CashbookPageState();
}

class _CashbookPageState extends State<CashbookPage> {
  late PlutoGridStateManager stateManager;
  int? cbSelectedyear;
  int selectedMonth = DateTime.now().month;
  bool dataloading = false;
  bool dataSaving = false;
  bool csvdownload = false;
  bool pdfdownload = false;

  List<PlutoRow> rows = [];

  final List<PlutoColumnGroup> columnGroups = [
    PlutoColumnGroup(
      title: 'RECEIPT',
      fields: [
        'do',
        'dateReceipt',
        'particularsReceipt',
        'sub',
        'lt_loan',
        'st_loan',
        'int',
        'mr_no',
        'bankReceipt',
        'cashReceipt',
      ],
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumnGroup(
      title: 'PAYMENT',
      fields: [
        'datePayment',
        'particularsPayment',
        'vo_no',
        'cheque_no',
        'bankPayment',
        'cashPayment',
      ],
    ),
  ];

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'DO',
      field: 'do',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'DATE',
      field: 'dateReceipt',
      type: PlutoColumnType.date(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'PARTICULARS',
      field: 'particularsReceipt',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'SUB',
      field: 'sub',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'LT_LOAN',
      field: 'lt_loan',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'ST_LOAN',
      field: 'st_loan',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'INT',
      field: 'int',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'MR_NO',
      field: 'mr_no',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'BANK',
      field: 'bankReceipt',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'CASH',
      field: 'cashReceipt',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'DATE',
      field: 'datePayment',
      type: PlutoColumnType.date(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'PARTICULARS',
      field: 'particularsPayment',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'VO_NO',
      field: 'vo_no',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'CHEQUE_NO',
      field: 'cheque_no',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'BANK',
      field: 'bankPayment',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'CASH',
      field: 'cashPayment',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
  ];

  final List<DateTime> last6Months = List.generate(6, (index) {
    final now = DateTime.now();
    final date = DateTime(now.year, now.month - index, 1);
    return date;
  }).reversed.toList();

  @override
  void initState() {
    super.initState();
    cbSelectedyear = TheFinancialYear.getCurrentYearForDatabase();
    insertRowsForMonth(cbSelectedyear!, selectedMonth);
  }

  Future<void> insertRowsForMonth(int year, int month) async {
    rows.clear();
    dataloading = true;
    setState(() {});

    try {
      final DateFormat formatter = DateFormat('yyyy-MM-dd');

      // 🔹 Instead of storing every user row, store totals per DO
      final Map<String, Map<String, num>> doTotals = {};

      final recoverySnap = await FBFireStore.usermonthly
          .where('selectedyear', isEqualTo: year)
          .where('selectedmonth', isEqualTo: month)
          .get();

      for (var snapdoc in recoverySnap.docs) {
        final data = snapdoc.data();

        final doNameList = Get.find<HomeCtrl>()
            .districtoffice
            .where((element) => element.docId == data['districtoffice'])
            .toList()
          ..sort((a, b) => a.name.compareTo(b.name));
        String doValue = '';
        if (doNameList.isNotEmpty) {
          doValue = doNameList.first.name;
        }

        // Initialize totals for DO if not exists
        doTotals.putIfAbsent(
            doValue,
            () => {
                  'sub': 0,
                  'lt_loan': 0,
                  'st_loan': 0,
                  'int': 0,
                  'bankReceipt': 0,
                  'cashReceipt': 0,
                  'bankPayment': 0,
                  'cashPayment': 0,
                });

        // Add values to DO totals
        doTotals[doValue]!['sub'] =
            (doTotals[doValue]!['sub'] ?? 0) + (data['subs'] ?? 0);
        doTotals[doValue]!['lt_loan'] = (doTotals[doValue]!['lt_loan'] ?? 0) +
            (data['longTermInstalmentPaid'] ?? 0);
        doTotals[doValue]!['st_loan'] = (doTotals[doValue]!['st_loan'] ?? 0) +
            (data['shortTermInstalmentPaid'] ?? 0);
        doTotals[doValue]!['int'] =
            (doTotals[doValue]!['int'] ?? 0) + (data['interest'] ?? 0);
        doTotals[doValue]!['bankReceipt'] =
            (doTotals[doValue]!['bankReceipt'] ?? 0) +
                (data['installmentRec'] ?? 0);
        // If you also track cashReceipt, bankPayment, cashPayment from other fields, sum them here
      }

      final sortedDoNames = doTotals.keys.toList()..sort();

      final List<PlutoRow> userRowsWithSingleEmptyRowsBetweenDO = [];
      for (final doName in sortedDoNames) {
        final vals = doTotals[doName]!;
        userRowsWithSingleEmptyRowsBetweenDO.add(
          PlutoRow(cells: {
            'do': PlutoCell(value: doName),
            'dateReceipt': PlutoCell(value: ''),
            'particularsReceipt': PlutoCell(value: ''),
            'sub': PlutoCell(value: vals['sub']),
            'lt_loan': PlutoCell(value: vals['lt_loan']),
            'st_loan': PlutoCell(value: vals['st_loan']),
            'int': PlutoCell(value: vals['int']),
            'mr_no': PlutoCell(value: ''),
            'bankReceipt': PlutoCell(value: vals['bankReceipt']),
            'cashReceipt': PlutoCell(value: vals['cashReceipt']),
            'datePayment': PlutoCell(value: ''),
            'particularsPayment': PlutoCell(value: ''),
            'vo_no': PlutoCell(value: ''),
            'cheque_no': PlutoCell(value: ''),
            'bankPayment': PlutoCell(value: vals['bankPayment']),
            'cashPayment': PlutoCell(value: vals['cashPayment']),
          }),
        );

        // Empty row after each DO
        userRowsWithSingleEmptyRowsBetweenDO.add(
          PlutoRow(cells: {
            for (var col in columns) col.field: PlutoCell(value: '')
          }),
        );
      }

      // Extra empty row after all DO rows
      userRowsWithSingleEmptyRowsBetweenDO.add(
        PlutoRow(
            cells: {for (var col in columns) col.field: PlutoCell(value: '')}),
      );

      // 🔹 Keep rest of your code exactly same
      final expenseSnap = await FBFireStore.expense
          .where('expenseDate',
              isGreaterThanOrEqualTo: DateTime(year, month, 1))
          .where('expenseDate', isLessThan: DateTime(year, month + 1, 1))
          .get();

      final Map<int, PlutoRow> incomeRowsByIndex = {};
      final Map<int, PlutoRow> expenseRowsByIndex = {};
      int incomeIndex = 0;
      int expenseIndex = 0;

      for (var snapdoc in expenseSnap.docs) {
        final expense = Expense.fromSnap(snapdoc);
        final expenseDateStr = formatter.format(expense.expenseDate);
        final cashValue = expense.cash ? expense.amount : 0;
        final bankValue = expense.cash ? 0 : expense.amount;

        if (expense.isIncome) {
          incomeRowsByIndex[incomeIndex] = PlutoRow(cells: {
            'do': PlutoCell(value: ''),
            'dateReceipt': PlutoCell(value: expenseDateStr),
            'particularsReceipt': PlutoCell(value: expense.name),
            'sub': PlutoCell(value: 0),
            'lt_loan': PlutoCell(value: 0),
            'st_loan': PlutoCell(value: 0),
            'int': PlutoCell(value: 0),
            'mr_no': PlutoCell(value: ''),
            'bankReceipt': PlutoCell(value: bankValue),
            'cashReceipt': PlutoCell(value: cashValue),
            'datePayment': PlutoCell(value: ''),
            'particularsPayment': PlutoCell(value: ''),
            'vo_no': PlutoCell(value: ''),
            'cheque_no': PlutoCell(value: ''),
            'bankPayment': PlutoCell(value: 0),
            'cashPayment': PlutoCell(value: 0),
          });
          incomeIndex++;
        } else {
          expenseRowsByIndex[expenseIndex] = PlutoRow(cells: {
            'do': PlutoCell(value: ''),
            'dateReceipt': PlutoCell(value: ''),
            'particularsReceipt': PlutoCell(value: ''),
            'sub': PlutoCell(value: 0),
            'lt_loan': PlutoCell(value: 0),
            'st_loan': PlutoCell(value: 0),
            'int': PlutoCell(value: 0),
            'mr_no': PlutoCell(value: ''),
            'bankReceipt': PlutoCell(value: 0),
            'cashReceipt': PlutoCell(value: 0),
            'datePayment': PlutoCell(value: expenseDateStr),
            'particularsPayment': PlutoCell(value: expense.name),
            'vo_no': PlutoCell(value: ''),
            'cheque_no': PlutoCell(value: ''),
            'bankPayment': PlutoCell(value: bankValue),
            'cashPayment': PlutoCell(value: cashValue),
          });
          expenseIndex++;
        }
      }

      final int maxRows =
          incomeIndex > expenseIndex ? incomeIndex : expenseIndex;
      final List<PlutoRow> alignedIncomeExpenseRows = [];

      for (int i = 0; i < maxRows; i++) {
        final incomeRow = incomeRowsByIndex[i];
        final expenseRow = expenseRowsByIndex[i];
        final Map<String, PlutoCell> combinedCells = {};

        for (var col in columns) {
          final field = col.field;

          if (incomeRow != null &&
              (field == 'do' ||
                  field == 'dateReceipt' ||
                  field == 'particularsReceipt' ||
                  field == 'sub' ||
                  field == 'lt_loan' ||
                  field == 'st_loan' ||
                  field == 'int' ||
                  field == 'mr_no' ||
                  field == 'bankReceipt' ||
                  field == 'cashReceipt')) {
            combinedCells[field] =
                incomeRow.cells[field] ?? PlutoCell(value: '');
          } else if (expenseRow != null &&
              (field == 'datePayment' ||
                  field == 'particularsPayment' ||
                  field == 'vo_no' ||
                  field == 'cheque_no' ||
                  field == 'bankPayment' ||
                  field == 'cashPayment')) {
            combinedCells[field] =
                expenseRow.cells[field] ?? PlutoCell(value: '');
          } else {
            combinedCells[field] = PlutoCell(value: '');
          }
        }

        alignedIncomeExpenseRows.add(PlutoRow(cells: combinedCells));
      }

      final txnSnap = await FBFireStore.transactions
          .where('createdAt', isGreaterThanOrEqualTo: DateTime(year, month, 1))
          .where('createdAt', isLessThan: DateTime(year, month + 1, 1))
          .where('loanId', isNotEqualTo: null)
          // .where('showInCB', isEqualTo: null)
          .where('showInCB', isEqualTo: true)
          .get();

      final filteredTxns = txnSnap.docs.where((doc) {
        final title = doc['title'] as String?;
        return title != null && title.endsWith(' Transaction');
      }).toList();

      final List<PlutoRow> transactionRows = [];
      for (var snapdoc in filteredTxns) {
        final txn = TransactionModel.fromSnap(snapdoc);

        if (txn.loanId == null) continue;
        if (!(txn.title.endsWith(' Transaction') ?? false)) continue;

        final txnDateStr = formatter.format(txn.createdAt);

        if (txn.inn == true) {
          transactionRows.add(
            PlutoRow(cells: {
              'do': PlutoCell(value: ''),
              'dateReceipt': PlutoCell(value: txnDateStr),
              'particularsReceipt': PlutoCell(value: txn.title),
              'sub': PlutoCell(value: 0),
              'lt_loan': PlutoCell(value: 0),
              'st_loan': PlutoCell(value: 0),
              'int': PlutoCell(value: 0),
              'mr_no': PlutoCell(value: ''),
              'bankReceipt': PlutoCell(value: txn.amount),
              'cashReceipt': PlutoCell(value: 0),
              'datePayment': PlutoCell(value: ''),
              'particularsPayment': PlutoCell(value: ''),
              'vo_no': PlutoCell(value: ''),
              'cheque_no': PlutoCell(value: ''),
              'bankPayment': PlutoCell(value: 0),
              'cashPayment': PlutoCell(value: 0),
            }),
          );
        } else if (txn.inn == false) {
          transactionRows.add(
            PlutoRow(cells: {
              'do': PlutoCell(value: ''),
              'dateReceipt': PlutoCell(value: ''),
              'particularsReceipt': PlutoCell(value: ''),
              'sub': PlutoCell(value: 0),
              'lt_loan': PlutoCell(value: 0),
              'st_loan': PlutoCell(value: 0),
              'int': PlutoCell(value: 0),
              'mr_no': PlutoCell(value: ''),
              'bankReceipt': PlutoCell(value: 0),
              'cashReceipt': PlutoCell(value: 0),
              'datePayment': PlutoCell(value: txnDateStr),
              'particularsPayment': PlutoCell(value: txn.title),
              'vo_no': PlutoCell(value: ''),
              'cheque_no': PlutoCell(value: ''),
              'bankPayment': PlutoCell(value: txn.amount),
              'cashPayment': PlutoCell(value: 0),
            }),
          );
        }
      }

      final payoutTxnSnap = await FBFireStore.transactions
          .where('createdAt', isGreaterThanOrEqualTo: DateTime(year, month, 1))
          .where('createdAt', isLessThan: DateTime(year, month + 1, 1))
          .where('loanId', isNotEqualTo: null)
          .get();

      final List<PlutoRow> payoutTransactionRows = [];

      for (var snapdoc in payoutTxnSnap.docs) {
        final title = snapdoc['title'] as String?;
        if (title != null &&
            (title == 'Subscription Payout' || title == 'Share Payout')) {
          final txn = TransactionModel.fromSnap(snapdoc);
          final txnDateStr = formatter.format(txn.createdAt);

          if (txn.inn == true) {
            payoutTransactionRows.add(
              PlutoRow(cells: {
                'do': PlutoCell(value: ''),
                'dateReceipt': PlutoCell(value: ''),
                'particularsReceipt': PlutoCell(value: ''),
                'sub': PlutoCell(value: 0),
                'lt_loan': PlutoCell(value: 0),
                'st_loan': PlutoCell(value: 0),
                'int': PlutoCell(value: 0),
                'mr_no': PlutoCell(value: ''),
                'bankReceipt': PlutoCell(value: 0),
                'cashReceipt': PlutoCell(value: 0),
                'datePayment': PlutoCell(value: txnDateStr),
                'particularsPayment': PlutoCell(value: txn.title),
                'vo_no': PlutoCell(value: ''),
                'cheque_no': PlutoCell(value: ''),
                'bankPayment': PlutoCell(value: txn.amount),
                'cashPayment': PlutoCell(value: 0),
              }),
            );
          }
        }
      }

      final loanTxn = await FBFireStore.transactions
          .where('createdAt', isGreaterThanOrEqualTo: DateTime(year, month, 1))
          .where('createdAt', isLessThan: DateTime(year, month + 1, 1))
          .get();

      Map<String, List<TransactionModel>> userTxns = {};

      print("loantxn : ${loanTxn.docs.length}");

      for (var snapdoc in loanTxn.docs) {
        final title = snapdoc['title'] as String?;
        if (title != null &&
            (title == 'Loan Approved' || title == 'Share Deduction 10%')) {
          final txn = TransactionModel.fromSnap(snapdoc);
          userTxns.putIfAbsent(txn.uId, () => []).add(txn);
        }
      }

      for (var entry in userTxns.entries) {
        final userId = entry.key;
        final txns = entry.value;

        // Fetch user by document ID instead of field
        final txnUserDoc = await FBFireStore.users.doc(userId).get();

        print("User lookup for $userId -> exists: ${txnUserDoc.exists}");

        if (!txnUserDoc.exists) continue;

        final userData = txnUserDoc.data()!;

        String doValue = '';
        final doNameList = Get.find<HomeCtrl>()
            .districtoffice
            .where((element) => element.docId == userData['districtoffice'])
            .toList();
        if (doNameList.isNotEmpty) {
          doValue = doNameList.first.name;
        }

        double loanApprovedAmount = 0;
        double shareDeductionAmount = 0;
        DateTime loanDate = DateTime.now();

        for (var txn in txns) {
          if (txn.title == 'Loan Approved') {
            loanApprovedAmount = txn.amount.toDouble();
            loanDate = txn.createdAt;
          } else if (txn.title == 'Share Deduction 10%') {
            shareDeductionAmount = txn.amount.toDouble();
          }
        }

        double netLoanToBank = loanApprovedAmount - shareDeductionAmount;
        final txnDateStr = formatter.format(loanDate);

        payoutTransactionRows.add(
          PlutoRow(cells: {
            'do': PlutoCell(value: doValue),
            'dateReceipt': PlutoCell(value: txnDateStr),
            'particularsReceipt': PlutoCell(value: userData['name']),
            'sub': PlutoCell(value: 0),
            'lt_loan': PlutoCell(value: 0),
            'st_loan': PlutoCell(value: 0),
            'int': PlutoCell(value: 0),
            'mr_no': PlutoCell(value: ''),
            'bankReceipt': PlutoCell(value: 0),
            'cashReceipt': PlutoCell(value: shareDeductionAmount),
            'datePayment': PlutoCell(value: txnDateStr),
            'particularsPayment': PlutoCell(value: userData['name']),
            'vo_no': PlutoCell(value: ''),
            'cheque_no': PlutoCell(value: ''),
            'bankPayment': PlutoCell(value: netLoanToBank),
            'cashPayment': PlutoCell(value: shareDeductionAmount),
          }),
        );
      }

      // Combine all rows
      rows = [
        ...userRowsWithSingleEmptyRowsBetweenDO,
        ...alignedIncomeExpenseRows,
        ...transactionRows,
        ...payoutTransactionRows,
      ];

      num sumColumn(List<PlutoRow> listRows, String field) {
        num total = 0;
        for (final r in listRows) {
          final cellValue = r.cells[field]?.value;
          if (cellValue is num) {
            total += cellValue;
          } else if (cellValue is String) {
            final parsed = num.tryParse(cellValue);
            if (parsed != null) total += parsed;
          }
        }
        return total;
      }

      final totalSub = sumColumn(rows, 'sub');
      final totalLtLoan = sumColumn(rows, 'lt_loan');
      final totalStLoan = sumColumn(rows, 'st_loan');
      final totalInt = sumColumn(rows, 'int');
      final totalBankReceipt = sumColumn(rows, 'bankReceipt');
      final totalCashReceipt = sumColumn(rows, 'cashReceipt');
      final totalBankPayment = sumColumn(rows, 'bankPayment');
      final totalCashPayment = sumColumn(rows, 'cashPayment');

      final totalRow = PlutoRow(cells: {
        'do': PlutoCell(value: 'Total'),
        'dateReceipt': PlutoCell(value: ''),
        'particularsReceipt': PlutoCell(value: ''),
        'sub': PlutoCell(value: totalSub),
        'lt_loan': PlutoCell(value: totalLtLoan),
        'st_loan': PlutoCell(value: totalStLoan),
        'int': PlutoCell(value: totalInt),
        'mr_no': PlutoCell(value: ''),
        'bankReceipt': PlutoCell(value: totalBankReceipt),
        'cashReceipt': PlutoCell(value: totalCashReceipt),
        'datePayment': PlutoCell(value: ''),
        'particularsPayment': PlutoCell(value: ''),
        'vo_no': PlutoCell(value: ''),
        'cheque_no': PlutoCell(value: ''),
        'bankPayment': PlutoCell(value: totalBankPayment),
        'cashPayment': PlutoCell(value: totalCashPayment),
      });

      final emptyRow = PlutoRow(
        cells: {for (var col in columns) col.field: PlutoCell(value: '')},
      );

      rows.add(emptyRow);
      rows.add(totalRow);
    } catch (e) {
      debugPrint('Error fetching data: $e');
    } finally {
      dataloading = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    // print("rows lenght :  ${rows.length}");
    return Padding(
      padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              DropdownButtonHideUnderline(
                child: DropdownButtonFormField<DateTime>(
                  focusColor: Colors.transparent,
                  dropdownColor: Colors.white,
                  value: last6Months.firstWhere(
                    (d) =>
                        d.month == selectedMonth &&
                        d.year == (cbSelectedyear ?? DateTime.now().year),
                    orElse: () => last6Months.last,
                  ),
                  decoration: InputDecoration(
                    hintText: "Select Month",
                    constraints:
                        const BoxConstraints(maxWidth: 150, maxHeight: 45),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                  items: last6Months.map((date) {
                    final formatted = "${month[date.month - 1]} ${date.year}";
                    return DropdownMenuItem<DateTime>(
                      value: date,
                      child: Text(formatted),
                    );
                  }).toList(),
                  onChanged: (DateTime? value) async {
                    if (value == null) return;

                    setState(() {
                      cbSelectedyear = value.year;
                      selectedMonth = value.month;
                      dataloading = true;
                    });

                    await insertRowsForMonth(cbSelectedyear!, selectedMonth);

                    setState(() {
                      dataloading = false;
                    });

                    if (rows.isEmpty) {
                      showCtcAppSnackBar(context, 'No data for this month');
                    }
                  },
                ),
              ),
              Row(
                children: [
                  csvdownload
                      ? CircularProgressIndicator()
                      : CustomHeaderButton(
                          onPressed: () async {
                            await exportToCSV();
                          },
                          buttonName: 'Export to CSV',
                        ),
                  const SizedBox(width: 5),
                  pdfdownload
                      ? CircularProgressIndicator()
                      : CustomHeaderButton(
                          onPressed: () async {
                            await exportToPDF();
                          },
                          buttonName: 'Export to PDF',
                        ),
                ],
              ),
            ],
          ),

          // Heading
          Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "CASHBOOK FOR THE YEAR ${cbSelectedyear ?? DateTime.now().year}",
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 16))),

          dataloading
              ? Center(child: const CircularProgressIndicator())
              : Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.transparent),
                    ),
                    child: PlutoGrid(
                      columnGroups: columnGroups,
                      columns: columns,
                      rows: rows,
                      onLoaded: (PlutoGridOnLoadedEvent event) async {
                        stateManager = event.stateManager;
                        stateManager.setShowColumnFilter(true);
                        setState(() {});
                      },
                      onChanged: (PlutoGridOnChangedEvent event) {},
                      configuration: const PlutoGridConfiguration(
                        columnSize: PlutoGridColumnSizeConfig(
                          autoSizeMode: PlutoAutoSizeMode.none,
                        ),
                        scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: false,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging,
                        ),
                      ),
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Future<void> exportToCSV() async {
    try {
      setState(() => csvdownload = true);

      final csv = StringBuffer();

      // Define headers similar to your Cashbook structure
      final headers = [
        'DO',
        'Receipt Date',
        'Particulars (Receipt)',
        'SUB',
        'LT_LOAN',
        'ST_LOAN',
        'INTEREST',
        'BANK Receipt',
        'CASH Receipt',
        'Payment Date',
        'Particulars (Payment)',
        'VO_NO',
        'CHEQUE_NO',
        'BANK Payment',
        'CASH Payment',
      ];

      // Write headers with quotes
      csv.writeln(headers.map((e) => '"$e"').join(','));

      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        setState(() => csvdownload = false);
        return;
      }

      for (var row in stateManager.rows) {
        final rowData = [
          row.cells['do']?.value?.toString() ?? '',
          row.cells['dateReceipt']?.value?.toString() ?? '',
          row.cells['particularsReceipt']?.value?.toString() ?? '',
          row.cells['sub']?.value?.toString() ?? '',
          row.cells['lt_loan']?.value?.toString() ?? '',
          row.cells['st_loan']?.value?.toString() ?? '',
          row.cells['int']?.value?.toString() ?? '',
          row.cells['bankReceipt']?.value?.toString() ?? '',
          row.cells['cashReceipt']?.value?.toString() ?? '',
          row.cells['datePayment']?.value?.toString() ?? '',
          row.cells['particularsPayment']?.value?.toString() ?? '',
          row.cells['vo_no']?.value?.toString() ?? '',
          row.cells['cheque_no']?.value?.toString() ?? '',
          row.cells['bankPayment']?.value?.toString() ?? '',
          row.cells['cashPayment']?.value?.toString() ?? '',
        ];

        // Quote every field to preserve commas/newlines
        final quotedRow = rowData.map((e) => '"${e.replaceAll('"', '""')}"');

        csv.writeln(quotedRow.join(','));
      }

      final csvBytes = html.Blob([csv.toString()], 'text/csv');
      final csvUrl = html.Url.createObjectUrlFromBlob(csvBytes);

      final anchor = html.AnchorElement(href: csvUrl)
        ..setAttribute(
            'download', 'cashbook_${cbSelectedyear}_$selectedMonth.csv')
        ..click();

      html.Url.revokeObjectUrl(csvUrl);

      showCtcAppSnackBar(context, "CSV downloaded successfully");
    } catch (e, stackTrace) {
      showCtcAppSnackBar(context, "Failed to export CSV: $e");
      // You might want to log for debugging:
      // print(stackTrace);
    } finally {
      setState(() => csvdownload = false);
    }
  }

  Future<void> exportToPDF() async {
    setState(() {
      pdfdownload = true;
    });
    try {
      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        setState(() {
          pdfdownload = false;
        });
        return;
      }

      final pdf = pw.Document();

      final headers = [
        'DO',
        'Receipt Date',
        'Particulars (Receipt)',
        'SUB',
        'LT_LOAN',
        'ST_LOAN',
        'INT',
        'BANK Receipt',
        'CASH Receipt',
        'Payment Date',
        'Particulars (Payment)',
        'VO_NO',
        'CHEQUE_NO',
        'BANK Payment',
        'CASH Payment'
      ];

      final data = stateManager.rows.map((row) {
        return [
          row.cells['do']?.value?.toString() ?? '',
          row.cells['dateReceipt']?.value?.toString() ?? '',
          row.cells['particularsReceipt']?.value?.toString() ?? '',
          row.cells['sub']?.value?.toString() ?? '',
          row.cells['lt_loan']?.value?.toString() ?? '',
          row.cells['st_loan']?.value?.toString() ?? '',
          row.cells['int']?.value?.toString() ?? '',
          row.cells['bankReceipt']?.value?.toString() ?? '',
          row.cells['cashReceipt']?.value?.toString() ?? '',
          row.cells['datePayment']?.value?.toString() ?? '',
          row.cells['particularsPayment']?.value?.toString() ?? '',
          row.cells['vo_no']?.value?.toString() ?? '',
          row.cells['cheque_no']?.value?.toString() ?? '',
          row.cells['bankPayment']?.value?.toString() ?? '',
          row.cells['cashPayment']?.value?.toString() ?? '',
        ];
      }).toList();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a3.landscape,
          margin: pw.EdgeInsets.all(10),
          build: (context) => [
            pw.Text(
              'CASHBOOK FOR THE YEAR $cbSelectedyear - Month $selectedMonth',
              style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            pw.Table.fromTextArray(
              headers: headers,
              data: data,
              cellAlignment: pw.Alignment.centerLeft,
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              border: pw.TableBorder.all(width: 0.5),
              cellPadding: const pw.EdgeInsets.all(5),
              columnWidths: {
                0: const pw.FixedColumnWidth(40),
                1: const pw.FixedColumnWidth(60),
                2: const pw.FixedColumnWidth(100),
                3: const pw.FixedColumnWidth(40),
                4: const pw.FixedColumnWidth(40),
                5: const pw.FixedColumnWidth(40),
                6: const pw.FixedColumnWidth(40),
                7: const pw.FixedColumnWidth(60),
                8: const pw.FixedColumnWidth(60),
                9: const pw.FixedColumnWidth(60),
                10: const pw.FixedColumnWidth(100),
                11: const pw.FixedColumnWidth(40),
                12: const pw.FixedColumnWidth(60),
                13: const pw.FixedColumnWidth(60),
                14: const pw.FixedColumnWidth(60),
              },
            ),
          ],
        ),
      );

      final bytes = await pdf.save();

      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute(
            'download', 'cashbook_${cbSelectedyear}_$selectedMonth.pdf')
        ..click();
      html.Url.revokeObjectUrl(url);
      setState(() {
        pdfdownload = false;
      });

      showCtcAppSnackBar(context, "PDF downloaded successfully");
    } catch (e) {
      setState(() {
        pdfdownload = false;
      });
      showCtcAppSnackBar(context, "Failed to export PDF: ${e.toString()}");
      // Optionally log: print(stackTrace);
    }
  }
}
